<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Variations extends Model
{
    use HasFactory;

    protected $table = 'variations';

    protected $fillable = [
        'id',
        'name',
        'product_id',
        'sub_sku',
        'product_variation_id',
        'woocommerce_variation_id',
        'variation_value_id',
        'default_purchase_price',
        'dpp_inc_tax',
        'profit_percent',
        'default_sell_price',
        'sell_price_inc_tax',
        'deleted_at',
        'combo_variations'
    ];

}
