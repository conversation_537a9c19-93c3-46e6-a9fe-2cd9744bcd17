<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('units', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->unsignedInteger('business_id');
            $table->string('actual_name', 191);
            $table->string('short_name', 191);
            $table->string('allow_decimal');
            $table->integer('base_unit_id')->nullable()->default('NULL');
            $table->decimal('base_unit_multiplier', 20,4)->nullable()->default('NULL');
            $table->unsignedInteger('created_by');
            $table->timestamp('deleted_at')->nullable()->default('NULL');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('units');
    }
};
