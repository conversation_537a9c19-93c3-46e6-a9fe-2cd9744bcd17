<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: user_comission
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_comission', function (Blueprint $table) {
            $table->integer('id');
            $table->date('date')->nullable();
            $table->string('username', 100)->nullable();
            $table->string('voucher', 100)->nullable();
            $table->string('product_id', 100)->nullable();
            $table->string('product_name', 100)->nullable();
            $table->decimal('discount', 10, 2)->nullable();
            $table->decimal('commission', 10, 2)->nullable();
            $table->string('com_type', 100)->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_comission');
    }
};
