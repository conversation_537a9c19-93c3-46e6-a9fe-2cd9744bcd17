<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: variation_group_prices
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('variation_group_prices', function (Blueprint $table) {
            $table->integer('id');
            $table->integer('variation_id');
            $table->integer('price_group_id');
            $table->decimal('price_inc_tax', 22, 4);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('variation_group_prices');
    }
};
