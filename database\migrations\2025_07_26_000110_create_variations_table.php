<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('variations', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->string('name', 191);
            $table->unsignedInteger('product_id');
            $table->string('sub_sku', 191)->nullable()->default('NULL');
            $table->unsignedInteger('product_variation_id');
            $table->integer('woocommerce_variation_id')->nullable()->default('NULL');
            $table->integer('variation_value_id')->nullable()->default('NULL');
            $table->decimal('default_purchase_price', 22,4)->nullable()->default('NULL');
            $table->decimal('dpp_inc_tax', 22,4)->default('0.0000');
            $table->decimal('profit_percent', 22,4)->default('0.0000');
            $table->decimal('default_sell_price', 22,4)->nullable()->default('NULL');
            $table->decimal('sell_price_inc_tax', 22,4)->nullable()->default('NULL');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
            $table->timestamp('deleted_at')->nullable()->default('NULL');
            $table->text('combo_variations')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('variations');
    }
};
