<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->unsignedInteger('business_id');
            $table->unsignedInteger('location_id')->nullable()->default('NULL');
            $table->unsignedInteger('res_table_id')->nullable()->default('NULL');
            $table->unsignedInteger('res_waiter_id')->nullable()->default('NULL');
            $table->enum('res_order_status')->nullable()->default('NULL');
            $table->string('type', 191)->nullable()->default('NULL');
            $table->string('sub_type', 20)->nullable()->default('NULL');
            $table->string('status', 191);
            $table->integer('p_refrence')->nullable()->default('0');
            $table->string('sub_status', 191)->nullable()->default('NULL');
            $table->string('is_quotation')->nullable()->default('0');
            $table->enum('payment_status')->nullable()->default('NULL');
            $table->enum('adjustment_type')->nullable()->default('NULL');
            $table->unsignedInteger('contact_id')->nullable()->default('NULL');
            $table->integer('customer_group_id')->nullable()->default('NULL');
            $table->string('invoice_no', 191)->nullable()->default('NULL');
            $table->string('ref_no', 191)->nullable()->default('NULL');
            $table->string('source', 191)->nullable()->default('NULL');
            $table->string('subscription_no', 191)->nullable()->default('NULL');
            $table->string('subscription_repeat_on', 191)->nullable()->default('NULL');
            $table->dateTime('transaction_date')->nullable()->default('NULL');
            $table->decimal('total_before_tax', 22,4)->nullable()->default('0.0000');
            $table->unsignedInteger('tax_id')->nullable()->default('NULL');
            $table->decimal('tax_amount', 22,4)->nullable()->default('0.0000');
            $table->enum('discount_type')->nullable()->default('NULL');
            $table->decimal('discount_amount', 22,4)->nullable()->default('0.0000');
            $table->integer('rp_redeemed')->nullable()->default('0');
            $table->decimal('rp_redeemed_amount', 22,4)->nullable()->default('0.0000');
            $table->string('shipping_details', 191)->nullable()->default('NULL');
            $table->text('shipping_address')->nullable()->default('NULL');
            $table->string('shipping_status', 191)->nullable()->default('NULL');
            $table->string('delivered_to', 191)->nullable()->default('NULL');
            $table->decimal('shipping_charges', 22,4)->nullable()->default('0.0000');
            $table->string('shipping_custom_field_1', 191)->nullable()->default('NULL');
            $table->string('shipping_custom_field_2', 191)->nullable()->default('NULL');
            $table->string('shipping_custom_field_3', 191)->nullable()->default('NULL');
            $table->string('shipping_custom_field_4', 191)->nullable()->default('NULL');
            $table->string('shipping_custom_field_5', 191)->nullable()->default('NULL');
            $table->text('additional_notes')->nullable()->default('NULL');
            $table->text('staff_note')->nullable()->default('NULL');
            $table->string('is_export')->nullable()->default('0');
            $table->longText('export_custom_fields_info')->nullable()->default('NULL');
            $table->decimal('round_off_amount', 22,4)->nullable()->default('0.0000');
            $table->string('additional_expense_key_1', 191)->nullable()->default('NULL');
            $table->decimal('additional_expense_value_1', 22,4)->nullable()->default('0.0000');
            $table->string('additional_expense_key_2', 191)->nullable()->default('NULL');
            $table->decimal('additional_expense_value_2', 22,4)->nullable()->default('0.0000');
            $table->string('additional_expense_key_3', 191)->nullable()->default('NULL');
            $table->decimal('additional_expense_value_3', 22,4)->nullable()->default('0.0000');
            $table->string('additional_expense_key_4', 191)->nullable()->default('NULL');
            $table->decimal('additional_expense_value_4', 22,4)->nullable()->default('0.0000');
            $table->decimal('final_total', 22,4)->nullable()->default('0.0000');
            $table->decimal('ab_ar', 22,4)->nullable()->default('0.0000');
            $table->unsignedInteger('expense_category_id')->nullable()->default('NULL');
            $table->integer('expense_sub_category_id')->nullable()->default('NULL');
            $table->unsignedInteger('expense_for')->nullable()->default('NULL');
            $table->integer('commission_agent')->nullable()->default('NULL');
            $table->string('document', 191)->nullable()->default('NULL');
            $table->string('is_direct_sale')->nullable()->default('0');
            $table->string('is_suspend')->nullable()->default('0');
            $table->decimal('exchange_rate', 20,3)->nullable()->default('1.000');
            $table->decimal('total_amount_recovered', 22,4)->nullable()->default('NULL');
            $table->integer('transfer_parent_id')->nullable()->default('NULL');
            $table->integer('return_parent_id')->nullable()->default('NULL');
            $table->integer('opening_stock_product_id')->nullable()->default('NULL');
            $table->unsignedInteger('created_by')->nullable()->default('NULL');
            $table->integer('mfg_parent_production_purchase_id')->nullable()->default('NULL');
            $table->decimal('mfg_wasted_units', 22,4)->nullable()->default('NULL');
            $table->decimal('mfg_production_cost', 22,4)->nullable()->default('0.0000');
            $table->string('mfg_is_final')->nullable()->default('0');
            $table->integer('woocommerce_order_id')->nullable()->default('NULL');
            $table->decimal('essentials_duration', 8,2)->nullable()->default('0.00');
            $table->string('essentials_duration_unit', 20)->nullable()->default('NULL');
            $table->decimal('essentials_amount_per_unit_duration', 22,4)->nullable()->default('0.0000');
            $table->text('essentials_allowances')->nullable()->default('NULL');
            $table->text('essentials_deductions')->nullable()->default('NULL');
            $table->string('prefer_payment_method', 191)->nullable()->default('NULL');
            $table->integer('prefer_payment_account')->nullable()->default('NULL');
            $table->text('sales_order_ids')->nullable()->default('NULL');
            $table->text('purchase_order_ids')->nullable()->default('NULL');
            $table->string('custom_field_1', 191)->nullable()->default('NULL');
            $table->string('custom_field_2', 191)->nullable()->default('NULL');
            $table->string('custom_field_3', 191)->nullable()->default('NULL');
            $table->string('custom_field_4', 191)->nullable()->default('NULL');
            $table->integer('import_batch')->nullable()->default('NULL');
            $table->dateTime('import_time')->nullable()->default('NULL');
            $table->integer('types_of_service_id')->nullable()->default('NULL');
            $table->decimal('packing_charge', 22,4)->nullable()->default('NULL');
            $table->enum('packing_charge_type')->nullable()->default('NULL');
            $table->text('service_custom_field_1')->nullable()->default('NULL');
            $table->text('service_custom_field_2')->nullable()->default('NULL');
            $table->text('service_custom_field_3')->nullable()->default('NULL');
            $table->text('service_custom_field_4')->nullable()->default('NULL');
            $table->text('service_custom_field_5')->nullable()->default('NULL');
            $table->text('service_custom_field_6')->nullable()->default('NULL');
            $table->string('is_created_from_api')->nullable()->default('0');
            $table->integer('rp_earned')->nullable()->default('0');
            $table->text('order_addresses')->nullable()->default('NULL');
            $table->string('is_recurring')->nullable()->default('0');
            $table->string('recur_interval')->nullable()->default('NULL');
            $table->enum('recur_interval_type')->nullable()->default('NULL');
            $table->integer('recur_repetitions')->nullable()->default('NULL');
            $table->dateTime('recur_stopped_on')->nullable()->default('NULL');
            $table->integer('recur_parent_id')->nullable()->default('NULL');
            $table->string('invoice_token', 191)->nullable()->default('NULL');
            $table->integer('pay_term_number')->nullable()->default('NULL');
            $table->enum('pay_term_type')->nullable()->default('NULL');
            $table->integer('selling_price_group_id')->nullable()->default('NULL');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
