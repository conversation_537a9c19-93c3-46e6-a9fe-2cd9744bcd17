<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransactionSellLines extends Model
{
    use HasFactory;

    protected $table = 'transaction_sell_lines';

    protected $fillable = [
        'id',
        'transaction_id',
        'product_id',
        'variation_id',
        'quantity',
        'mfg_waste_percent',
        'quantity_returned',
        'unit_price_before_discount',
        'unit_price',
        'line_discount_type',
        'line_discount_amount',
        'unit_price_inc_tax',
        'item_tax',
        'tax_id',
        'discount_id',
        'lot_no_line_id',
        'sell_line_note',
        'woocommerce_line_items_id',
        'so_line_id',
        'so_quantity_invoiced',
        'res_service_staff_id',
        'res_line_order_status',
        'parent_sell_line_id',
        'children_type',
        'sub_unit_id'
    ];

}
