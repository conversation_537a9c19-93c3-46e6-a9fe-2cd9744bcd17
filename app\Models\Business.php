<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Business extends Model
{
    use HasFactory;

    protected $table = 'business';

    protected $fillable = [
        'id',
        'name',
        'currency_id',
        'start_date',
        'tax_number_1',
        'tax_label_1',
        'tax_number_2',
        'tax_label_2',
        'code_label_1',
        'code_1',
        'code_label_2',
        'code_2',
        'default_sales_tax',
        'default_profit_percent',
        'owner_id',
        'time_zone',
        'fy_start_month',
        'accounting_method',
        'default_sales_discount',
        'sell_price_tax',
        'logo',
        'sku_prefix',
        'enable_product_expiry',
        'expiry_type',
        'on_product_expiry',
        'stop_selling_before',
        'enable_tooltip',
        'purchase_in_diff_currency',
        'purchase_currency_id',
        'p_exchange_rate',
        'transaction_edit_days',
        'stock_expiry_alert_days',
        'keyboard_shortcuts',
        'pos_settings',
        'manufacturing_settings',
        'woocommerce_api_settings',
        'woocommerce_wh_oc_secret',
        'woocommerce_wh_ou_secret',
        'woocommerce_wh_od_secret',
        'woocommerce_wh_or_secret',
        'essentials_settings',
        'weighing_scale_setting',
        'enable_brand',
        'enable_category',
        'enable_sub_category',
        'enable_price_tax',
        'enable_purchase_status',
        'enable_lot_number',
        'default_unit',
        'enable_sub_units',
        'enable_racks',
        'enable_row',
        'enable_position',
        'enable_editing_product_from_purchase',
        'sales_cmsn_agnt',
        'item_addition_method',
        'enable_inline_tax',
        'currency_symbol_placement',
        'enabled_modules',
        'date_format',
        'time_format',
        'ref_no_prefixes',
        'theme_color',
        'created_by',
        'enable_rp',
        'rp_name',
        'amount_for_unit_rp',
        'min_order_total_for_rp',
        'max_rp_per_order',
        'redeem_amount_per_unit_rp',
        'min_order_total_for_redeem',
        'min_redeem_point',
        'max_redeem_point',
        'rp_expiry_period',
        'rp_expiry_type',
        'email_settings',
        'sms_settings',
        'custom_labels',
        'common_settings',
        'is_active'
    ];

}
