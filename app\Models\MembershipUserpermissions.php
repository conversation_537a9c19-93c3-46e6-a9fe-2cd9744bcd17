<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MembershipUserpermissions extends Model
{
    use HasFactory;

    protected $table = 'membership_userpermissions';

    protected $fillable = [
        'permissionID',
        'memberID',
        'tableName',
        'allowInsert',
        'allowView',
        'allowEdit',
        'allowDelete'
    ];

    public $timestamps = false;

}
