<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MembershipUsers extends Model
{
    use HasFactory;

    protected $table = 'membership_users';

    protected $fillable = [
        'memberID',
        'passMD5',
        'email',
        'signupDate',
        'groupID',
        'isBanned',
        'isApproved',
        'custom1',
        'custom2',
        'custom3',
        'custom4',
        'comments',
        'pass_reset_key',
        'pass_reset_expiry',
        'flags',
        'allowCSVImport',
        'data',
        'sponsor',
        'alert_amount',
        'sponsor_rate'
    ];

    public $timestamps = false;

}
