<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: stock_adjustments_temp
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('stock_adjustments_temp', function (Blueprint $table) {
            $table->integer('id')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stock_adjustments_temp');
    }
};
