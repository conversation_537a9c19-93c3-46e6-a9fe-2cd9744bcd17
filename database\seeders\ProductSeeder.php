<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = [
            [
                'sku' => 'AUTO001',
                'name' => 'Engine Oil 5W-30',
                'description' => 'High-quality synthetic engine oil',
                'category' => 'Engine Oil',
                'brand' => 'Mobil',
                'price' => 45.99,
                'cost_price' => 30.00,
                'stock_quantity' => 100,
                'min_stock_level' => 10,
                'unit' => 'bottle',
                'status' => 'active',
                'track_inventory' => true,
            ],
            [
                'sku' => 'AUTO002',
                'name' => 'Brake Pads Set',
                'description' => 'Premium ceramic brake pads',
                'category' => 'Brakes',
                'brand' => 'Brembo',
                'price' => 89.99,
                'cost_price' => 60.00,
                'stock_quantity' => 50,
                'min_stock_level' => 5,
                'unit' => 'set',
                'status' => 'active',
                'track_inventory' => true,
            ],
            [
                'sku' => 'AUTO003',
                'name' => 'Air Filter',
                'description' => 'High-performance air filter',
                'category' => 'Filters',
                'brand' => 'K&N',
                'price' => 25.99,
                'cost_price' => 15.00,
                'stock_quantity' => 75,
                'min_stock_level' => 15,
                'unit' => 'piece',
                'status' => 'active',
                'track_inventory' => true,
            ],
            [
                'sku' => 'AUTO004',
                'name' => 'Spark Plugs Set',
                'description' => 'Iridium spark plugs set of 4',
                'category' => 'Ignition',
                'brand' => 'NGK',
                'price' => 32.99,
                'cost_price' => 20.00,
                'stock_quantity' => 30,
                'min_stock_level' => 8,
                'unit' => 'set',
                'status' => 'active',
                'track_inventory' => true,
            ],
            [
                'sku' => 'AUTO005',
                'name' => 'Car Battery',
                'description' => '12V automotive battery',
                'category' => 'Electrical',
                'brand' => 'Optima',
                'price' => 149.99,
                'cost_price' => 100.00,
                'stock_quantity' => 20,
                'min_stock_level' => 3,
                'unit' => 'piece',
                'status' => 'active',
                'track_inventory' => true,
            ],
        ];

        foreach ($products as $product) {
            Product::create($product);
        }
    }
}
