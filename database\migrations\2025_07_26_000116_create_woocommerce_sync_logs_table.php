<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('woocommerce_sync_logs', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->integer('business_id');
            $table->string('sync_type', 191);
            $table->string('operation_type', 191)->nullable()->default('NULL');
            $table->longText('data')->nullable()->default('NULL');
            $table->longText('details')->nullable()->default('NULL');
            $table->integer('created_by');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('woocommerce_sync_logs');
    }
};
