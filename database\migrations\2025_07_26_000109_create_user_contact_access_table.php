<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_contact_access', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->integer('user_id');
            $table->integer('contact_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_contact_access');
    }
};
