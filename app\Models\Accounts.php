<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Accounts extends Model
{
    use HasFactory;

    protected $table = 'accounts';

    protected $fillable = [
        'id',
        'business_id',
        'name',
        'account_number',
        'account_details',
        'account_type_id',
        'note',
        'created_by',
        'is_closed',
        'deleted_at'
    ];

}
