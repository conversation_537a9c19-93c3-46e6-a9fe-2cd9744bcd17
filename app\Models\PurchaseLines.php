<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseLines extends Model
{
    use HasFactory;

    protected $table = 'purchase_lines';

    protected $fillable = [
        'id',
        'transaction_id',
        'product_id',
        'variation_id',
        'quantity',
        'pp_without_discount',
        'discount_percent',
        'purchase_price',
        'purchase_price_inc_tax',
        'item_tax',
        'tax_id',
        'purchase_order_line_id',
        'quantity_sold',
        'quantity_adjusted',
        'quantity_returned',
        'po_quantity_purchased',
        'mfg_quantity_used',
        'mfg_date',
        'exp_date',
        'lot_number',
        'sub_unit_id'
    ];

}
