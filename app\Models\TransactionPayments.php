<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransactionPayments extends Model
{
    use HasFactory;

    protected $table = 'transaction_payments';

    protected $fillable = [
        'id',
        'transaction_id',
        'business_id',
        'is_return',
        'amount',
        'method',
        'transaction_no',
        'card_transaction_number',
        'card_number',
        'card_type',
        'card_holder_name',
        'card_month',
        'card_year',
        'card_security',
        'cheque_number',
        'bank_account_number',
        'paid_on',
        'p_refrence',
        'created_by',
        'paid_through_link',
        'gateway',
        'is_advance',
        'payment_for',
        'parent_id',
        'note',
        'document',
        'payment_ref_no',
        'account_id'
    ];

}
