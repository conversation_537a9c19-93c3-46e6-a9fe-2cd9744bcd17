<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountTransactions extends Model
{
    use HasFactory;

    protected $table = 'account_transactions';

    protected $fillable = [
        'id',
        'account_id',
        'type',
        'sub_type',
        'amount',
        'reff_no',
        'operation_date',
        'created_by',
        'transaction_id',
        'transaction_payment_id',
        'transfer_transaction_id',
        'note',
        'deleted_at'
    ];

}
