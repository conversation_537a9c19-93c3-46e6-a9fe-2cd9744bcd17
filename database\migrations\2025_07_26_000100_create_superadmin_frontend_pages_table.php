<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: superadmin_frontend_pages
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('superadmin_frontend_pages', function (Blueprint $table) {
            $table->integer('id');
            $table->string('title', 191)->nullable();
            $table->string('slug', 191);
            $table->longText('content');
            $table->boolean('is_shown');
            $table->integer('menu_order')->nullable()->default('0');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('superadmin_frontend_pages');
    }
};
