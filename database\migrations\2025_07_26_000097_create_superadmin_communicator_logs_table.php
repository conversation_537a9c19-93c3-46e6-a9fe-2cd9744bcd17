<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('superadmin_communicator_logs', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->text('business_ids')->nullable()->default('NULL');
            $table->string('subject', 191)->nullable()->default('NULL');
            $table->text('message')->nullable()->default('NULL');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('superadmin_communicator_logs');
    }
};
