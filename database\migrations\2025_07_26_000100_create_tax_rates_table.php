<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('tax_rates', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->unsignedInteger('business_id');
            $table->string('name', 191);
            $table->string('amount');
            $table->string('is_tax_group')->default('0');
            $table->string('for_tax_group')->default('0');
            $table->unsignedInteger('created_by');
            $table->timestamp('deleted_at')->nullable()->default('NULL');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tax_rates');
    }
};
