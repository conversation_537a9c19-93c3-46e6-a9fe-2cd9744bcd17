<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Bookings extends Model
{
    use HasFactory;

    protected $table = 'bookings';

    protected $fillable = [
        'id',
        'contact_id',
        'waiter_id',
        'table_id',
        'correspondent_id',
        'business_id',
        'location_id',
        'booking_start',
        'booking_end',
        'created_by',
        'booking_status',
        'booking_note'
    ];

}
