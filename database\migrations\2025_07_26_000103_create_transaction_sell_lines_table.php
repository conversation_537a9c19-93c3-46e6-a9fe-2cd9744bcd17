<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('transaction_sell_lines', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->unsignedInteger('transaction_id');
            $table->unsignedInteger('product_id')->nullable()->default('NULL');
            $table->unsignedInteger('variation_id')->nullable()->default('NULL');
            $table->decimal('quantity', 22,4)->nullable()->default('0.0000');
            $table->decimal('mfg_waste_percent', 22,4)->nullable()->default('0.0000');
            $table->decimal('quantity_returned', 20,4)->nullable()->default('0.0000');
            $table->decimal('unit_price_before_discount', 22,4)->nullable()->default('0.0000');
            $table->decimal('unit_price', 22,4)->nullable()->default('NULL');
            $table->enum('line_discount_type')->nullable()->default('NULL');
            $table->decimal('line_discount_amount', 22,4)->nullable()->default('0.0000');
            $table->decimal('unit_price_inc_tax', 22,4)->nullable()->default('NULL');
            $table->decimal('item_tax', 22,4)->nullable()->default('NULL');
            $table->unsignedInteger('tax_id')->nullable()->default('NULL');
            $table->integer('discount_id')->nullable()->default('NULL');
            $table->integer('lot_no_line_id')->nullable()->default('NULL');
            $table->text('sell_line_note')->nullable()->default('NULL');
            $table->integer('woocommerce_line_items_id')->nullable()->default('NULL');
            $table->integer('so_line_id')->nullable()->default('NULL');
            $table->decimal('so_quantity_invoiced', 22,4)->nullable()->default('0.0000');
            $table->integer('res_service_staff_id')->nullable()->default('NULL');
            $table->string('res_line_order_status', 191)->nullable()->default('NULL');
            $table->integer('parent_sell_line_id')->nullable()->default('NULL');
            $table->string('children_type', 191)->nullable()->default('NULL');
            $table->integer('sub_unit_id')->nullable()->default('NULL');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transaction_sell_lines');
    }
};
