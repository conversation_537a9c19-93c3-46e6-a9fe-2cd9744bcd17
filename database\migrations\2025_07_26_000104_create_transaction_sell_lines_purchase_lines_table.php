<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('transaction_sell_lines_purchase_lines', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->unsignedInteger('sell_line_id')->nullable()->default('NULL');
            $table->unsignedInteger('stock_adjustment_line_id')->nullable()->default('NULL');
            $table->unsignedInteger('purchase_line_id');
            $table->decimal('quantity', 22,4);
            $table->decimal('qty_returned', 22,4)->default('0.0000');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transaction_sell_lines_purchase_lines');
    }
};
