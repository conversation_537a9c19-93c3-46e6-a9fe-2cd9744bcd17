<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: transaction_sell_lines_purchase_lines
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('transaction_sell_lines_purchase_lines', function (Blueprint $table) {
            $table->integer('id');
            $table->integer('sell_line_id')->nullable();
            $table->integer('stock_adjustment_line_id')->nullable();
            $table->integer('purchase_line_id');
            $table->decimal('quantity', 22, 4);
            $table->decimal('qty_returned', 22, 4)->default('0.0000');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transaction_sell_lines_purchase_lines');
    }
};
