<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Packages extends Model
{
    use HasFactory;

    protected $table = 'packages';

    protected $fillable = [
        'id',
        'name',
        'description',
        'location_count',
        'user_count',
        'product_count',
        'bookings',
        'kitchen',
        'order_screen',
        'tables',
        'invoice_count',
        'interval',
        'interval_count',
        'trial_days',
        'price',
        'custom_permissions',
        'created_by',
        'sort_order',
        'is_active',
        'is_private',
        'is_one_time',
        'enable_custom_link',
        'custom_link',
        'custom_link_text',
        'deleted_at'
    ];

}
