<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: wallet
 * Source: Autostore Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('wallet', function (Blueprint $table) {
            $table->integer('id');
            $table->date('date')->nullable();
            $table->string('transaction', 40)->nullable();
            $table->string('reason', 100)->nullable();
            $table->decimal('balance_before', 10, 2)->nullable();
            $table->decimal('balance_after', 10, 2)->nullable();
            $table->string('points', 40)->nullable();
            $table->string('username', 40)->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('wallet');
    }
};
