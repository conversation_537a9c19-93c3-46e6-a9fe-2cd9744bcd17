<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: transaction_sell_lines
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('transaction_sell_lines', function (Blueprint $table) {
            $table->integer('id');
            $table->integer('transaction_id');
            $table->integer('product_id')->nullable();
            $table->integer('variation_id')->nullable();
            $table->decimal('quantity', 22, 4)->nullable()->default('0.0000');
            $table->decimal('mfg_waste_percent', 22, 4)->nullable()->default('0.0000');
            $table->decimal('quantity_returned', 20, 4)->nullable()->default('0.0000');
            $table->decimal('unit_price_before_discount', 22, 4)->nullable()->default('0.0000');
            $table->decimal('unit_price', 22, 4)->nullable();
            $table->enum('line_discount_type', ['fixed','percentage'])->nullable();
            $table->decimal('line_discount_amount', 22, 4)->nullable()->default('0.0000');
            $table->decimal('unit_price_inc_tax', 22, 4)->nullable();
            $table->decimal('item_tax', 22, 4)->nullable();
            $table->integer('tax_id')->nullable();
            $table->integer('discount_id')->nullable();
            $table->integer('lot_no_line_id')->nullable();
            $table->text('sell_line_note')->nullable();
            $table->integer('woocommerce_line_items_id')->nullable();
            $table->integer('so_line_id')->nullable();
            $table->decimal('so_quantity_invoiced', 22, 4)->nullable()->default('0.0000');
            $table->integer('res_service_staff_id')->nullable();
            $table->string('res_line_order_status', 191)->nullable();
            $table->integer('parent_sell_line_id')->nullable();
            $table->string('children_type', 191)->nullable();
            $table->integer('sub_unit_id')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transaction_sell_lines');
    }
};
