<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_comission', function (Blueprint $table) {
            $table->integer('id');
            $table->date('date')->nullable()->default('NULL');
            $table->string('username', 100)->nullable()->default('NULL');
            $table->string('voucher', 100)->nullable()->default('NULL');
            $table->string('product_id', 100)->nullable()->default('NULL');
            $table->string('product_name', 100)->nullable()->default('NULL');
            $table->string('discount')->nullable()->default('NULL');
            $table->string('commission')->nullable()->default('NULL');
            $table->string('com_type', 100)->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_comission');
    }
};
