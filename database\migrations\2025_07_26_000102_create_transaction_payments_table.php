<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('transaction_payments', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->unsignedInteger('transaction_id')->nullable()->default('NULL');
            $table->integer('business_id')->nullable()->default('NULL');
            $table->string('is_return')->default('0');
            $table->decimal('amount', 22,4)->default('0.0000');
            $table->string('method', 191)->nullable()->default('NULL');
            $table->string('transaction_no', 191)->nullable()->default('NULL');
            $table->string('card_transaction_number', 191)->nullable()->default('NULL');
            $table->string('card_number', 191)->nullable()->default('NULL');
            $table->string('card_type', 191)->nullable()->default('NULL');
            $table->string('card_holder_name', 191)->nullable()->default('NULL');
            $table->string('card_month', 191)->nullable()->default('NULL');
            $table->string('card_year', 191)->nullable()->default('NULL');
            $table->string('card_security', 5)->nullable()->default('NULL');
            $table->string('cheque_number', 191)->nullable()->default('NULL');
            $table->string('bank_account_number', 191)->nullable()->default('NULL');
            $table->dateTime('paid_on')->nullable()->default('NULL');
            $table->string('p_refrence', 50)->nullable()->default('NULL');
            $table->integer('created_by')->nullable()->default('NULL');
            $table->string('paid_through_link')->nullable()->default('0');
            $table->string('gateway', 191)->nullable()->default('NULL');
            $table->string('is_advance')->nullable()->default('0');
            $table->integer('payment_for')->nullable()->default('NULL');
            $table->integer('parent_id')->nullable()->default('NULL');
            $table->string('note', 191)->nullable()->default('NULL');
            $table->string('document', 191)->nullable()->default('NULL');
            $table->string('payment_ref_no', 191)->nullable()->default('NULL');
            $table->integer('account_id')->nullable()->default('NULL');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transaction_payments');
    }
};
