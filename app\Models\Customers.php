<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customers extends Model
{
    use HasFactory;

    protected $table = 'customers';

    protected $fillable = [
        'id',
        'order_id',
        'date',
        'name',
        'email',
        'mobile1',
        'mobile2',
        'city',
        'address',
        'brand',
        'model',
        'status',
        'source'
    ];

    public $timestamps = false;

}
