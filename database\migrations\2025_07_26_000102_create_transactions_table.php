<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: transactions
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->integer('id');
            $table->integer('business_id');
            $table->integer('location_id')->nullable();
            $table->integer('res_table_id')->nullable();
            $table->integer('res_waiter_id')->nullable();
            $table->enum('res_order_status', ['received','cooked','served'])->nullable();
            $table->string('type', 191)->nullable();
            $table->string('sub_type', 20)->nullable();
            $table->string('status', 191);
            $table->integer('p_refrence')->nullable()->default('0');
            $table->string('sub_status', 191)->nullable();
            $table->boolean('is_quotation')->nullable()->default('0');
            $table->enum('payment_status', ['paid','due','partial'])->nullable();
            $table->enum('adjustment_type', ['normal','abnormal'])->nullable();
            $table->integer('contact_id')->nullable();
            $table->integer('customer_group_id')->nullable();
            $table->string('invoice_no', 191)->nullable();
            $table->string('ref_no', 191)->nullable();
            $table->string('source', 191)->nullable();
            $table->string('subscription_no', 191)->nullable();
            $table->string('subscription_repeat_on', 191)->nullable();
            $table->dateTime('transaction_date')->nullable();
            $table->decimal('total_before_tax', 22, 4)->nullable()->default('0.0000');
            $table->integer('tax_id')->nullable();
            $table->decimal('tax_amount', 22, 4)->nullable()->default('0.0000');
            $table->enum('discount_type', ['fixed','percentage'])->nullable();
            $table->decimal('discount_amount', 22, 4)->nullable()->default('0.0000');
            $table->integer('rp_redeemed')->nullable()->default('0');
            $table->decimal('rp_redeemed_amount', 22, 4)->nullable()->default('0.0000');
            $table->string('shipping_details', 191)->nullable();
            $table->text('shipping_address')->nullable();
            $table->string('shipping_status', 191)->nullable();
            $table->string('delivered_to', 191)->nullable();
            $table->decimal('shipping_charges', 22, 4)->nullable()->default('0.0000');
            $table->string('shipping_custom_field_1', 191)->nullable();
            $table->string('shipping_custom_field_2', 191)->nullable();
            $table->string('shipping_custom_field_3', 191)->nullable();
            $table->string('shipping_custom_field_4', 191)->nullable();
            $table->string('shipping_custom_field_5', 191)->nullable();
            $table->text('additional_notes')->nullable();
            $table->text('staff_note')->nullable();
            $table->boolean('is_export')->nullable()->default('0');
            $table->longText('export_custom_fields_info')->nullable();
            $table->decimal('round_off_amount', 22, 4)->nullable()->default('0.0000');
            $table->string('additional_expense_key_1', 191)->nullable();
            $table->decimal('additional_expense_value_1', 22, 4)->nullable()->default('0.0000');
            $table->string('additional_expense_key_2', 191)->nullable();
            $table->decimal('additional_expense_value_2', 22, 4)->nullable()->default('0.0000');
            $table->string('additional_expense_key_3', 191)->nullable();
            $table->decimal('additional_expense_value_3', 22, 4)->nullable()->default('0.0000');
            $table->string('additional_expense_key_4', 191)->nullable();
            $table->decimal('additional_expense_value_4', 22, 4)->nullable()->default('0.0000');
            $table->decimal('final_total', 22, 4)->nullable()->default('0.0000');
            $table->decimal('ab_ar', 22, 4)->nullable()->default('0.0000');
            $table->integer('expense_category_id')->nullable();
            $table->integer('expense_sub_category_id')->nullable();
            $table->integer('expense_for')->nullable();
            $table->integer('commission_agent')->nullable();
            $table->string('document', 191)->nullable();
            $table->boolean('is_direct_sale')->nullable()->default('0');
            $table->boolean('is_suspend')->nullable()->default('0');
            $table->decimal('exchange_rate', 20, 3)->nullable()->default('1.000');
            $table->decimal('total_amount_recovered', 22, 4)->nullable();
            $table->integer('transfer_parent_id')->nullable();
            $table->integer('return_parent_id')->nullable();
            $table->integer('opening_stock_product_id')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('mfg_parent_production_purchase_id')->nullable();
            $table->decimal('mfg_wasted_units', 22, 4)->nullable();
            $table->decimal('mfg_production_cost', 22, 4)->nullable()->default('0.0000');
            $table->boolean('mfg_is_final')->nullable()->default('0');
            $table->integer('woocommerce_order_id')->nullable();
            $table->decimal('essentials_duration', 8, 2)->nullable()->default('0.00');
            $table->string('essentials_duration_unit', 20)->nullable();
            $table->decimal('essentials_amount_per_unit_duration', 22, 4)->nullable()->default('0.0000');
            $table->text('essentials_allowances')->nullable();
            $table->text('essentials_deductions')->nullable();
            $table->string('prefer_payment_method', 191)->nullable();
            $table->integer('prefer_payment_account')->nullable();
            $table->text('sales_order_ids')->nullable();
            $table->text('purchase_order_ids')->nullable();
            $table->string('custom_field_1', 191)->nullable();
            $table->string('custom_field_2', 191)->nullable();
            $table->string('custom_field_3', 191)->nullable();
            $table->string('custom_field_4', 191)->nullable();
            $table->integer('import_batch')->nullable();
            $table->dateTime('import_time')->nullable();
            $table->integer('types_of_service_id')->nullable();
            $table->decimal('packing_charge', 22, 4)->nullable();
            $table->enum('packing_charge_type', ['fixed','percent'])->nullable();
            $table->text('service_custom_field_1')->nullable();
            $table->text('service_custom_field_2')->nullable();
            $table->text('service_custom_field_3')->nullable();
            $table->text('service_custom_field_4')->nullable();
            $table->text('service_custom_field_5')->nullable();
            $table->text('service_custom_field_6')->nullable();
            $table->boolean('is_created_from_api')->nullable()->default('0');
            $table->integer('rp_earned')->nullable()->default('0');
            $table->text('order_addresses')->nullable();
            $table->boolean('is_recurring')->nullable()->default('0');
            $table->double('recur_interval')->nullable();
            $table->enum('recur_interval_type', ['days','months','years'])->nullable();
            $table->integer('recur_repetitions')->nullable();
            $table->dateTime('recur_stopped_on')->nullable();
            $table->integer('recur_parent_id')->nullable();
            $table->string('invoice_token', 191)->nullable();
            $table->integer('pay_term_number')->nullable();
            $table->enum('pay_term_type', ['days','months'])->nullable();
            $table->integer('selling_price_group_id')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
