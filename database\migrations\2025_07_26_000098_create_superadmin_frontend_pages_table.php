<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('superadmin_frontend_pages', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->string('title', 191)->nullable()->default('NULL');
            $table->string('slug', 191);
            $table->longText('content');
            $table->string('is_shown');
            $table->integer('menu_order')->nullable()->default('0');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('superadmin_frontend_pages');
    }
};
