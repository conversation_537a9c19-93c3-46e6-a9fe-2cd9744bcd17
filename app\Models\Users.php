<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Users extends Model
{
    use HasFactory;

    protected $table = 'users';

    protected $fillable = [
        'id',
        'user_type',
        'surname',
        'first_name',
        'last_name',
        'username',
        'email',
        'password',
        'language',
        'contact_no',
        'address',
        'remember_token',
        'business_id',
        'essentials_department_id',
        'essentials_designation_id',
        'essentials_salary',
        'essentials_pay_period',
        'essentials_pay_cycle',
        'max_sales_discount_percent',
        'allow_login',
        'status',
        'crm_contact_id',
        'is_cmmsn_agnt',
        'cmmsn_percent',
        'selected_contacts',
        'dob',
        'gender',
        'marital_status',
        'blood_group',
        'contact_number',
        'alt_number',
        'family_number',
        'fb_link',
        'twitter_link',
        'social_media_1',
        'social_media_2',
        'permanent_address',
        'current_address',
        'guardian_name',
        'custom_field_1',
        'custom_field_2',
        'custom_field_3',
        'custom_field_4',
        'bank_details',
        'id_proof_name',
        'id_proof_number',
        'deleted_at'
    ];

}
