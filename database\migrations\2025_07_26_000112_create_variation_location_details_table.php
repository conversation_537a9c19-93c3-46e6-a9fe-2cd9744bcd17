<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('variation_location_details', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->unsignedInteger('product_id');
            $table->unsignedInteger('product_variation_id');
            $table->unsignedInteger('variation_id');
            $table->unsignedInteger('location_id');
            $table->decimal('qty_available', 22,4)->default('0.0000');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('variation_location_details');
    }
};
