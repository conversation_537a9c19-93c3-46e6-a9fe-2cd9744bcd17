<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: woocommerce_sync_logs
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('woocommerce_sync_logs', function (Blueprint $table) {
            $table->integer('id');
            $table->integer('business_id');
            $table->string('sync_type', 191);
            $table->string('operation_type', 191)->nullable();
            $table->longText('data')->nullable();
            $table->longText('details')->nullable();
            $table->integer('created_by');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('woocommerce_sync_logs');
    }
};
