<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: customers
 * Source: Autostore Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->integer('id');
            $table->string('order_id', 40)->nullable();
            $table->date('date')->nullable();
            $table->string('name', 55)->nullable();
            $table->string('email', 55)->nullable();
            $table->string('mobile1', 40)->nullable();
            $table->string('mobile2', 40)->nullable();
            $table->string('city', 40)->nullable();
            $table->text('address')->nullable();
            $table->string('brand', 40)->nullable();
            $table->string('model', 40)->nullable();
            $table->string('status', 40)->nullable();
            $table->string('source', 55)->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
