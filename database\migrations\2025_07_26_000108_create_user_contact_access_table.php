<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: user_contact_access
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_contact_access', function (Blueprint $table) {
            $table->integer('id');
            $table->integer('user_id');
            $table->integer('contact_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_contact_access');
    }
};
