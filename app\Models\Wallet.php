<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Wallet extends Model
{
    use HasFactory;

    protected $table = 'wallet';

    protected $fillable = [
        'id',
        'date',
        'transaction',
        'reason',
        'balance_before',
        'balance_after',
        'points',
        'username',
        'alert_amount'
    ];

    public $timestamps = false;

}
