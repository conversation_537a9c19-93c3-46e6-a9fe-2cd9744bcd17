<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('variation_group_prices', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->unsignedInteger('variation_id');
            $table->unsignedInteger('price_group_id');
            $table->decimal('price_inc_tax', 22,4);
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('variation_group_prices');
    }
};
