<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transactions extends Model
{
    use HasFactory;

    protected $table = 'transactions';

    protected $fillable = [
        'id',
        'business_id',
        'location_id',
        'res_table_id',
        'res_waiter_id',
        'res_order_status',
        'type',
        'sub_type',
        'status',
        'p_refrence',
        'sub_status',
        'is_quotation',
        'payment_status',
        'adjustment_type',
        'contact_id',
        'customer_group_id',
        'invoice_no',
        'ref_no',
        'source',
        'subscription_no',
        'subscription_repeat_on',
        'transaction_date',
        'total_before_tax',
        'tax_id',
        'tax_amount',
        'discount_type',
        'discount_amount',
        'rp_redeemed',
        'rp_redeemed_amount',
        'shipping_details',
        'shipping_address',
        'shipping_status',
        'delivered_to',
        'shipping_charges',
        'shipping_custom_field_1',
        'shipping_custom_field_2',
        'shipping_custom_field_3',
        'shipping_custom_field_4',
        'shipping_custom_field_5',
        'additional_notes',
        'staff_note',
        'is_export',
        'export_custom_fields_info',
        'round_off_amount',
        'additional_expense_key_1',
        'additional_expense_value_1',
        'additional_expense_key_2',
        'additional_expense_value_2',
        'additional_expense_key_3',
        'additional_expense_value_3',
        'additional_expense_key_4',
        'additional_expense_value_4',
        'final_total',
        'ab_ar',
        'expense_category_id',
        'expense_sub_category_id',
        'expense_for',
        'commission_agent',
        'document',
        'is_direct_sale',
        'is_suspend',
        'exchange_rate',
        'total_amount_recovered',
        'transfer_parent_id',
        'return_parent_id',
        'opening_stock_product_id',
        'created_by',
        'mfg_parent_production_purchase_id',
        'mfg_wasted_units',
        'mfg_production_cost',
        'mfg_is_final',
        'woocommerce_order_id',
        'essentials_duration',
        'essentials_duration_unit',
        'essentials_amount_per_unit_duration',
        'essentials_allowances',
        'essentials_deductions',
        'prefer_payment_method',
        'prefer_payment_account',
        'sales_order_ids',
        'purchase_order_ids',
        'custom_field_1',
        'custom_field_2',
        'custom_field_3',
        'custom_field_4',
        'import_batch',
        'import_time',
        'types_of_service_id',
        'packing_charge',
        'packing_charge_type',
        'service_custom_field_1',
        'service_custom_field_2',
        'service_custom_field_3',
        'service_custom_field_4',
        'service_custom_field_5',
        'service_custom_field_6',
        'is_created_from_api',
        'rp_earned',
        'order_addresses',
        'is_recurring',
        'recur_interval',
        'recur_interval_type',
        'recur_repetitions',
        'recur_stopped_on',
        'recur_parent_id',
        'invoice_token',
        'pay_term_number',
        'pay_term_type',
        'selling_price_group_id'
    ];

}
