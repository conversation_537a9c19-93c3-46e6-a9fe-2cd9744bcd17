<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Discounts extends Model
{
    use HasFactory;

    protected $table = 'discounts';

    protected $fillable = [
        'id',
        'name',
        'business_id',
        'brand_id',
        'category_id',
        'location_id',
        'priority',
        'discount_type',
        'discount_amount',
        'starts_at',
        'ends_at',
        'is_active',
        'spg',
        'applicable_in_cg'
    ];

}
