<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Requests extends Model
{
    use HasFactory;

    protected $table = 'requests';

    protected $fillable = [
        'id',
        'date',
        'time',
        'order_id',
        'bosta_id',
        'name',
        'email',
        'mobile',
        'mobile2',
        'city',
        'address',
        'brand',
        'model',
        'product1',
        'price1',
        'product2',
        'price2',
        'delivery',
        'total_price',
        'paid',
        'due',
        'status',
        'source',
        'invoice_num',
        'comment',
        'upload'
    ];

    public $timestamps = false;

}
