<?php

namespace Database\Seeders;

use App\Models\Customer;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '******-0101',
                'address' => '123 Main Street',
                'city' => 'New York',
                'state' => 'NY',
                'postal_code' => '10001',
                'country' => 'USA',
                'status' => 'active',
                'credit_limit' => 5000.00,
                'current_balance' => 0.00,
                'customer_group' => 'retail',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '******-0102',
                'address' => '456 Oak Avenue',
                'city' => 'Los Angeles',
                'state' => 'CA',
                'postal_code' => '90001',
                'country' => 'USA',
                'status' => 'active',
                'credit_limit' => 3000.00,
                'current_balance' => 0.00,
                'customer_group' => 'retail',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '******-0103',
                'address' => '789 Pine Street',
                'city' => 'Chicago',
                'state' => 'IL',
                'postal_code' => '60601',
                'country' => 'USA',
                'status' => 'active',
                'credit_limit' => 10000.00,
                'current_balance' => 0.00,
                'customer_group' => 'wholesale',
            ],
            [
                'name' => 'Emily Davis',
                'email' => '<EMAIL>',
                'phone' => '******-0104',
                'address' => '321 Elm Street',
                'city' => 'Houston',
                'state' => 'TX',
                'postal_code' => '77001',
                'country' => 'USA',
                'status' => 'active',
                'credit_limit' => 2500.00,
                'current_balance' => 0.00,
                'customer_group' => 'retail',
            ],
            [
                'name' => 'Auto Parts Plus',
                'email' => '<EMAIL>',
                'phone' => '******-0105',
                'address' => '555 Business Blvd',
                'city' => 'Miami',
                'state' => 'FL',
                'postal_code' => '33101',
                'country' => 'USA',
                'status' => 'active',
                'credit_limit' => 25000.00,
                'current_balance' => 0.00,
                'customer_group' => 'wholesale',
            ],
        ];

        foreach ($customers as $customer) {
            Customer::create($customer);
        }
    }
}
