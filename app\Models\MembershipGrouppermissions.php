<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MembershipGrouppermissions extends Model
{
    use HasFactory;

    protected $table = 'membership_grouppermissions';

    protected $fillable = [
        'permissionID',
        'groupID',
        'tableName',
        'allowInsert',
        'allowView',
        'allowEdit',
        'allowDelete'
    ];

    public $timestamps = false;

}
