<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: system
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('system', function (Blueprint $table) {
            $table->integer('id');
            $table->string('key', 191);
            $table->text('value')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('system');
    }
};
