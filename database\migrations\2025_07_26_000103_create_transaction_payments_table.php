<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: transaction_payments
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('transaction_payments', function (Blueprint $table) {
            $table->integer('id');
            $table->integer('transaction_id')->nullable();
            $table->integer('business_id')->nullable();
            $table->boolean('is_return')->default('0');
            $table->decimal('amount', 22, 4)->default('0.0000');
            $table->string('method', 191)->nullable();
            $table->string('transaction_no', 191)->nullable();
            $table->string('card_transaction_number', 191)->nullable();
            $table->string('card_number', 191)->nullable();
            $table->string('card_type', 191)->nullable();
            $table->string('card_holder_name', 191)->nullable();
            $table->string('card_month', 191)->nullable();
            $table->string('card_year', 191)->nullable();
            $table->string('card_security', 5)->nullable();
            $table->string('cheque_number', 191)->nullable();
            $table->string('bank_account_number', 191)->nullable();
            $table->dateTime('paid_on')->nullable();
            $table->string('p_refrence', 50)->nullable();
            $table->integer('created_by')->nullable();
            $table->boolean('paid_through_link')->nullable()->default('0');
            $table->string('gateway', 191)->nullable();
            $table->boolean('is_advance')->nullable()->default('0');
            $table->integer('payment_for')->nullable();
            $table->integer('parent_id')->nullable();
            $table->string('note', 191)->nullable();
            $table->string('document', 191)->nullable();
            $table->string('payment_ref_no', 191)->nullable();
            $table->integer('account_id')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transaction_payments');
    }
};
