<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'date',
        'name',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'status',
        'credit_limit',
        'current_balance',
        'customer_group',
        'notes',
    ];

    protected $casts = [
        'date' => 'date',
        'credit_limit' => 'decimal:2',
        'current_balance' => 'decimal:2',
    ];

    /**
     * Get all orders for this customer
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get customer's total order value
     */
    public function getTotalOrderValueAttribute(): float
    {
        return $this->orders()->sum('total_amount');
    }

    /**
     * Get customer's order count
     */
    public function getOrderCountAttribute(): int
    {
        return $this->orders()->count();
    }

    /**
     * Check if customer is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Scope for active customers
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for customers by group
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('customer_group', $group);
    }
}
