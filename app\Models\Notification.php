<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    protected $table = 'notification';

    protected $fillable = [
        'id',
        'date',
        'day',
        'subject',
        'discribtion',
        'status',
        'username',
        'stat',
        'user1',
        'change_from',
        'change_to'
    ];

    public $timestamps = false;

}
