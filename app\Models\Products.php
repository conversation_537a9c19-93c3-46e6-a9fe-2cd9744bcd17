<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Products extends Model
{
    use HasFactory;

    protected $table = 'products';

    protected $fillable = [
        'id',
        'name',
        'business_id',
        'type',
        'unit_id',
        'sub_unit_ids',
        'brand_id',
        'category_id',
        'sub_category_id',
        'tax',
        'tax_type',
        'enable_stock',
        'alert_quantity',
        'sku',
        'barcode_type',
        'expiry_period',
        'expiry_period_type',
        'enable_sr_no',
        'weight',
        'product_custom_field1',
        'product_custom_field2',
        'product_custom_field3',
        'product_custom_field4',
        'image',
        'woocommerce_media_id',
        'woocommerce_product_id',
        'woocommerce_disable_sync',
        'product_description',
        'created_by',
        'warranty_id',
        'is_inactive',
        'not_for_selling',
        'date',
        'category',
        'product_name',
        'product_price',
        'product_pic',
        'status'
    ];

}
