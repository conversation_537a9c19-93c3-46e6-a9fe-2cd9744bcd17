<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EssentialsToDos extends Model
{
    use HasFactory;

    protected $table = 'essentials_to_dos';

    protected $fillable = [
        'id',
        'business_id',
        'task',
        'date',
        'end_date',
        'task_id',
        'description',
        'status',
        'estimated_hours',
        'priority',
        'created_by'
    ];

}
