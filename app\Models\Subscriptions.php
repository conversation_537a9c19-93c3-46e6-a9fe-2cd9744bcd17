<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscriptions extends Model
{
    use HasFactory;

    protected $table = 'subscriptions';

    protected $fillable = [
        'id',
        'business_id',
        'package_id',
        'start_date',
        'trial_end_date',
        'end_date',
        'package_price',
        'package_details',
        'created_id',
        'paid_via',
        'payment_transaction_id',
        'status',
        'deleted_at'
    ];

}
