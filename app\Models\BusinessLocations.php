<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BusinessLocations extends Model
{
    use HasFactory;

    protected $table = 'business_locations';

    protected $fillable = [
        'id',
        'business_id',
        'location_id',
        'name',
        'landmark',
        'country',
        'state',
        'city',
        'zip_code',
        'invoice_scheme_id',
        'invoice_layout_id',
        'sale_invoice_layout_id',
        'selling_price_group_id',
        'print_receipt_on_invoice',
        'receipt_printer_type',
        'printer_id',
        'mobile',
        'alternate_number',
        'email',
        'website',
        'featured_products',
        'is_active',
        'default_payment_accounts',
        'custom_field1',
        'custom_field2',
        'custom_field3',
        'custom_field4',
        'deleted_at'
    ];

}
