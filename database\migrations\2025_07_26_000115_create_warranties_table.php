<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warranties', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->string('name', 191);
            $table->integer('business_id');
            $table->text('description')->nullable()->default('NULL');
            $table->integer('duration');
            $table->enum('duration_type');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warranties');
    }
};
