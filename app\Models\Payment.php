<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;

    protected $table = 'payment';

    protected $fillable = [
        'id',
        'date',
        'order_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'address',
        'item',
        'amount',
        'currency',
        'invoice_num',
        'payment_link',
        'status'
    ];

    public $timestamps = false;

}
