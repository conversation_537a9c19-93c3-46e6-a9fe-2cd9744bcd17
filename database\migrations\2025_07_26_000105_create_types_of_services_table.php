<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('types_of_services', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->string('name', 191);
            $table->text('description')->nullable()->default('NULL');
            $table->integer('business_id');
            $table->text('location_price_group')->nullable()->default('NULL');
            $table->decimal('packing_charge', 22,4)->nullable()->default('NULL');
            $table->enum('packing_charge_type')->nullable()->default('NULL');
            $table->string('enable_custom_fields')->default('0');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('types_of_services');
    }
};
