<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for table: types_of_services
 * Source: Main System Database
 * Generated automatically from existing database structure
 */
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('types_of_services', function (Blueprint $table) {
            $table->integer('id');
            $table->string('name', 191);
            $table->text('description')->nullable();
            $table->integer('business_id');
            $table->text('location_price_group')->nullable();
            $table->decimal('packing_charge', 22, 4)->nullable();
            $table->enum('packing_charge_type', ['fixed','percent'])->nullable();
            $table->boolean('enable_custom_fields')->default('0');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('types_of_services');
    }
};
