<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->string('user_type', 191)->default('user');
            $table->string('surname')->nullable()->default('NULL');
            $table->string('first_name', 191);
            $table->string('last_name', 191)->nullable()->default('NULL');
            $table->string('username', 191)->nullable()->default('NULL');
            $table->string('email', 191)->nullable()->default('NULL');
            $table->string('password', 191)->nullable()->default('NULL');
            $table->string('language')->default('en');
            $table->string('contact_no')->nullable()->default('NULL');
            $table->text('address')->nullable()->default('NULL');
            $table->string('remember_token', 100)->nullable()->default('NULL');
            $table->unsignedInteger('business_id')->nullable()->default('NULL');
            $table->integer('essentials_department_id')->nullable()->default('NULL');
            $table->integer('essentials_designation_id')->nullable()->default('NULL');
            $table->decimal('essentials_salary', 22,4)->nullable()->default('NULL');
            $table->string('essentials_pay_period', 191)->nullable()->default('NULL');
            $table->string('essentials_pay_cycle', 191)->nullable()->default('NULL');
            $table->decimal('max_sales_discount_percent', 5,2)->nullable()->default('NULL');
            $table->string('allow_login')->default('1');
            $table->enum('status')->default('active');
            $table->unsignedInteger('crm_contact_id')->nullable()->default('NULL');
            $table->string('is_cmmsn_agnt')->default('0');
            $table->decimal('cmmsn_percent', 4,2)->default('0.00');
            $table->string('selected_contacts')->default('0');
            $table->date('dob')->nullable()->default('NULL');
            $table->string('gender', 191)->nullable()->default('NULL');
            $table->enum('marital_status')->nullable()->default('NULL');
            $table->string('blood_group')->nullable()->default('NULL');
            $table->string('contact_number')->nullable()->default('NULL');
            $table->string('alt_number', 191)->nullable()->default('NULL');
            $table->string('family_number', 191)->nullable()->default('NULL');
            $table->string('fb_link', 191)->nullable()->default('NULL');
            $table->string('twitter_link', 191)->nullable()->default('NULL');
            $table->string('social_media_1', 191)->nullable()->default('NULL');
            $table->string('social_media_2', 191)->nullable()->default('NULL');
            $table->text('permanent_address')->nullable()->default('NULL');
            $table->text('current_address')->nullable()->default('NULL');
            $table->string('guardian_name', 191)->nullable()->default('NULL');
            $table->string('custom_field_1', 191)->nullable()->default('NULL');
            $table->string('custom_field_2', 191)->nullable()->default('NULL');
            $table->string('custom_field_3', 191)->nullable()->default('NULL');
            $table->string('custom_field_4', 191)->nullable()->default('NULL');
            $table->longText('bank_details')->nullable()->default('NULL');
            $table->string('id_proof_name', 191)->nullable()->default('NULL');
            $table->string('id_proof_number', 191)->nullable()->default('NULL');
            $table->timestamp('deleted_at')->nullable()->default('NULL');
            $table->timestamp('created_at')->nullable()->default('NULL');
            $table->timestamp('updated_at')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
