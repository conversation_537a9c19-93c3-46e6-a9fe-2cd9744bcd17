<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CashRegisters extends Model
{
    use HasFactory;

    protected $table = 'cash_registers';

    protected $fillable = [
        'id',
        'business_id',
        'location_id',
        'user_id',
        'ab_ar',
        'trans_id',
        'status',
        'closed_at',
        'closing_amount',
        'total_card_slips',
        'total_cheques',
        'denominations',
        'closing_note'
    ];

}
