<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('system', function (Blueprint $table) {
            $table->unsignedInteger('id');
            $table->string('key', 191);
            $table->text('value')->nullable()->default('NULL');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('system');
    }
};
